<script setup lang="ts">
import NoteDrawer from '@/components/NoteDrawer.vue';
import ComfirmDialog from '@/view/other/linkKlg/ComfirmDialog.vue';
import QuestionDetailDrawer from '@/components/QuestionDetailDrawer.vue';
import { emitter } from '@/utils/emitter';
import { Event } from '@/types/event';
import {
  getKlgDetailApi,
  getProofBlockListApi,
  getKlgRefListApi,
  getQuesDetailApi
} from '@/apis/path/klg';
import type { AreaListItem, ProofListItem, RefListItem } from '@/utils/type';
import { onMounted, ref, watch, shallowRef, toRaw, onUnmounted, triggerRef, nextTick } from 'vue';
import { findKeyByValue } from '@/utils/func';
import { KlgType, KlgTypeDict, WorkerType, klgAuditType } from '@/utils/constant';
import { storeToRefs } from 'pinia';
import { useDrawerControllerStore } from '@/stores/drawerController';
import { getQuestionListApi, type QuestionData } from '@/apis/path/klg';

import { userInfoStore } from '@/stores/userInfo';
import { handleProof, transferList2ProofList } from '@/utils/lineWordFunction';
import { convertMathTagsToMDLatex } from '@/utils/latexUtils';
// 引入新的 composables
import { useQuestionIcon } from '@/composables/useQuestionIcon';
import { useRenderManager } from '@/composables/useRenderManager';
import { useDrawerManager } from '@/composables/useDrawerManager';

const userStore = userInfoStore();

const drawerControllerStore = useDrawerControllerStore();
const { mode } = storeToRefs(drawerControllerStore);
const curWordModeText = ref();
const wordContent = ref();
const questionList = shallowRef<QuestionData[]>();

// 使用抽屉管理 composable
const {
  showAnswerDrawer,
  currentQuestionData,
  componentZIndex,
  initializeEventListeners,
  cleanupEventListeners,
  handleShowQuestionFromFloating
} = useDrawerManager();

// 使用问题图标管理 composable
const {
  questionIconVisible,
  questionIconPosition,
  showQuestionIcon,
  handleQuestionIconClick,
  handleDocumentClick,
  questionIconElement
} = useQuestionIcon({
  onIconClick: (selectedText: string) => {
    handleAddQuestion(selectedText);
  }
});

// 使用Render管理器
const { initializeRender, addQuestion, removeQuestion } = useRenderManager({
  containerSelector: '#underline',
  getContentData: () => wordContent.value || '',
  questionList,
  onSelect: (data: any) => {
    // 当选中文本时，显示问号图标而不是直接打开弹窗
    if (data && data.content) {
      showQuestionIcon(data);
    } else {
      console.log('❌ 选中文本为空或无效');
    }
  },
  onClick: (data: any) => {
    console.log('🔍 RenderManager onClick 事件触发:', data);
    // 直接触发CustomEvent，统一弹窗处理逻辑
    const event = new CustomEvent('showAnswerDrawer', {
      detail: { questionData: data.target }
    });
    window.dispatchEvent(event);
  },
  onFinish: (arg: any) => {
    const content = arg.content;
    console.log('myonFinish', content);
    wordContent.value = content;
  },
  enableDebugLog: true
});

// 证明过程的Render管理器（如果需要）
let proofRenderManager: any = null;

const props = defineProps({
  editable: Boolean,
  klgCode: String
});
const dataForm = ref({
  klgCode: '',
  title: '',
  type: -1,
  author: '',
  saveTime: '',
  synList: [] as String[],
  areaList: [] as AreaListItem[],
  refList: [] as RefListItem[],
  proofList: [] as ProofListItem[],
  note: '',
  cnt: ''
});
const drawerRef = ref();
const curStatus = ref();

const handleQuestionList = async (klgCode: string) => {
  // 获取问题列表
  const res = await getQuestionListApi(klgCode);
  questionList.value = res.data;

  // 使用RenderManager初始化主内容的render实例
  await initializeRender();

  // 如果是原理类型，处理证明过程
  if (dataForm.value.type === KlgType.Principles) {
    const underLineProofHtml = handleProof(dataForm.value.proofList);
    console.log('underLineProofHtml', underLineProofHtml);

    // 等待DOM更新
    await nextTick();

    // 初始化证明过程的RenderManager
    if (!proofRenderManager) {
      const { useRenderManager } = await import('@/composables/useRenderManager');
      proofRenderManager = useRenderManager({
        containerSelector: '.proof-block-list',
        getContentData: () => underLineProofHtml,
        questionList,
        onSelect: (data: any) => {
          if (data && data.content) {
            showQuestionIcon(data);
          } else {
            console.log('❌ 证明过程选中文本为空或无效');
          }
        },
        onClick: (data: any) => {
          const event = new CustomEvent('showAnswerDrawer', {
            detail: { questionData: data.target }
          });
          window.dispatchEvent(event);
        },
        enableDebugLog: true
      });

      await proofRenderManager.initializeRender();
    }
  }
};

// 获取klg详情
const getKlgDetail = async (): Promise<Boolean> => {
  let flag = false;
  try {
    if (props.klgCode) {
      const res = await getKlgDetailApi(props.klgCode);
      // console.log("获取klg详情res:", res)
      if (res.success) {
        emitter.emit(Event.SET_STATUS, res.data.detail.status);
        curStatus.value = res.data.detail.status;
        dataForm.value.klgCode = res.data.detail.klgCode;
        dataForm.value.title = res.data.detail.title;
        dataForm.value.type = res.data.detail.sortId;
        dataForm.value.cnt = res.data.detail.cnt;

        wordContent.value = dataForm.value.cnt; //对文稿做处理
        // wordStore.setConstContent(dataForm.value.cnt)

        dataForm.value.note = res.data.detail.notice;
        dataForm.value.author = res.data.detail.whoName;
        dataForm.value.saveTime = res.data.detail.modifiedTime;
        //dataForm.value.processedCnt = res.data.detail.processedCnt
        if (res.data.detail.sysTitles !== '') {
          dataForm.value.synList = res.data.detail.sysTitles.split('@@');
        }
        dataForm.value.areaList = res.data.areaList.map((item: any) => {
          return {
            areaCode: item.areaCode,
            label: item.title
          };
        });

        if (dataForm.value.type === KlgType.Principles) {
          await getProofList().then((result) => {
            if (result) {
              handleQuestionList(dataForm.value.klgCode);
              flag = true;
            }
          });
        } else {
          handleQuestionList(dataForm.value.klgCode);
          flag = true;
        }
      }
    }
  } catch (error) {
    console.log(error);
  }
  return flag;
};
// 获取审核信息
const getAuditInfo = () => {
  if (props.klgCode) {
    getKlgRefListApi(props.klgCode).then((res) => {
      if (res.success) {
        dataForm.value.refList = res.data.klgToRefVoList;
      }
    });
  }
};
// 获取论证块列表
const getProofList = async (): Promise<Boolean> => {
  if (props.klgCode) {
    const res = await getProofBlockListApi(props.klgCode);
    if (res.success) {
      dataForm.value.proofList = res.data.klgProofBlocks;
      return true;
    }
  }
  return false;
};
// 问号图标相关函数已移至 useQuestionIcon composable

// 处理添加问题
const handleAddQuestion = (words: string) => {
  const data = {
    mode: 1,
    associatedWords: convertMathTagsToMDLatex(words),
    keyword: convertMathTagsToMDLatex(words),
    klgCode: dataForm.value.klgCode,
    answers: [] // 添加空的answers数组，避免ComfirmDialog中的undefined错误
  };
  handleQuesDialog(data);
};
// 处理打开drawer
const handleDrawer = () => {
  drawerRef.value.showDrawer(dataForm.value.note);
};
// 处理重新
const handleReEdit = () => {
  window.open(`/editklg?klgCode=${dataForm.value.klgCode}`, '_self');
};

// 划词功能已通过 useRenderManager composable 管理
const handleQuesDialog = (data: any) => {
  emitter.emit(Event.SHOW_QUESTION_DIALOG, data);
};

// 浮动弹窗相关函数已移除，使用新的 QuestionDetailDrawer 组件
// 增加问题
const addQuestionFn = (data: any) => {
  // 使用RenderManager的addQuestion方法添加问题
  addQuestion(data.associatedWords, Number(data.questionId));

  // 如果是原理类型，也处理证明过程
  if (dataForm.value.type === KlgType.Principles && proofRenderManager) {
    proofRenderManager.addQuestion(data.associatedWords, Number(data.questionId));
  }

  // 更新questionList
  questionList.value?.push({
    questionId: data.questionId,
    associatedWords: data.associatedWords
  });

  emitter.emit(Event.REFRESH_QUESTION, true);
};

// 删除问题
const removeQuestionFn = (questionId: string) => {
  // 找到要删除的问题
  const rawQuestionList = toRaw(questionList.value);
  const questionIndex = rawQuestionList?.findIndex((item) => item.questionId == questionId);

  if (questionIndex !== undefined && questionIndex !== -1 && rawQuestionList) {
    const questionToRemove = rawQuestionList[questionIndex];
    console.log('删除问题:', questionToRemove);

    // 使用RenderManager的removeQuestion方法删除问题
    removeQuestion(questionToRemove.associatedWords, Number(questionId));

    // 如果是原理类型，也处理证明过程
    if (dataForm.value.type === KlgType.Principles && proofRenderManager) {
      proofRenderManager.removeQuestion(questionToRemove.associatedWords, Number(questionId));
    }

    // 从问题列表中移除
    rawQuestionList.splice(questionIndex, 1);
    triggerRef(questionList);

    console.log('问题删除完成');
  }
};
// 抽屉点击事件监听已移除，使用新的 QuestionDetailDrawer 组件

// 处理 showAnswerDrawer 自定义事件 - 显示问题详情抽屉
const handleShowAnswerDrawer = (event: CustomEvent) => {
  const { questionData } = event.detail;

  // 从 questionData 元素中获取 data-qid
  const qidAttr = questionData.getAttribute('data-qid');
  if (!qidAttr) {
    console.log('❌ 没有找到 data-qid 属性');
    return;
  }

  const qids = qidAttr.split(',');

  // 获取问题详情并显示新的问题详情抽屉
  getQuesDetailApi(qids)
    .then((res) => {
      // 触发显示问题详情抽屉的事件
      const showDetailEvent = new CustomEvent('showQuestionDetail', {
        detail: { questions: res.data.list }
      });
      window.dispatchEvent(showDetailEvent);
      console.log('✅ 问题详情抽屉已显示');
    })
    .catch((error) => {
      console.error('❌ 获取问题详情失败:', error);
    });
};

// 处理从问题详情抽屉显示答案抽屉的事件
const handleShowQuestionFromFloatingEvent = (event: CustomEvent) => {
  const { question } = event.detail;
  handleShowQuestionFromFloating(question);
};

onMounted(async () => {
  // 注册事件监听
  emitter.on(Event.ADD_QUESTION, addQuestionFn);
  emitter.on(Event.REMOVE_QUESTION, removeQuestionFn);

  // 初始化抽屉管理器的事件监听（包括 showAnswerDrawer）
  initializeEventListeners();

  // 添加 KlgInfo 特有的问题列表浮窗事件监听
  window.addEventListener('showAnswerDrawer', handleShowAnswerDrawer as EventListener);

  // 添加从问题详情抽屉显示答案抽屉的事件监听
  window.addEventListener(
    'showQuestionFromFloating',
    handleShowQuestionFromFloatingEvent as EventListener
  );

  // 添加问号图标的全局点击事件监听
  document.addEventListener('click', handleDocumentClick as EventListener, true);

  // 等待DOM渲染完成
  await nextTick();
});

onUnmounted(() => {
  // 移除事件监听
  emitter.off(Event.ADD_QUESTION, addQuestionFn);
  emitter.off(Event.REMOVE_QUESTION, removeQuestionFn);

  // 清理抽屉管理器的事件监听
  cleanupEventListeners();

  // 移除 KlgInfo 特有的问题列表浮窗事件监听
  window.removeEventListener('showAnswerDrawer', handleShowAnswerDrawer as EventListener);

  // 移除从问题详情抽屉显示答案抽屉的事件监听
  window.removeEventListener(
    'showQuestionFromFloating',
    handleShowQuestionFromFloatingEvent as EventListener
  );

  // 移除问号图标的全局点击事件监听
  document.removeEventListener('click', handleDocumentClick as EventListener, true);

  // RenderManager会自动清理render实例，无需手动清理
});

watch(
  () => props,
  () => {
    if (props.klgCode && props.klgCode !== '' && props.klgCode !== null) {
      getKlgDetail();
      getAuditInfo();
    }
  },
  { deep: true, immediate: true }
);

// 观察划词模式转换（现在只用于UI显示）
watch(
  () => mode,
  (newValue) => {
    curWordModeText.value = newValue.value === true ? '提问模式' : '展示模式';
  },
  { deep: true, immediate: true }
);
defineExpose({
  handleQuestionList,
  questionList
});
</script>
<template>
  <div class="wrapper">
    <div class="up-wrapper">
      <div class="header">
        <span class="header-left">
          <span class="header-title"><span class="ck-content" v-html="dataForm.title"></span></span>

          <span class="header-type">{{ findKeyByValue(dataForm.type, KlgTypeDict) }}</span>
        </span>
        <span
          class="header-right"
          v-if="
            curStatus !== klgAuditType.published &&
            curStatus !== klgAuditType.reviewing &&
            curStatus !== klgAuditType.pending &&
            dataForm.author === userStore.getUsername()
          "
          @click="handleReEdit"
        >
          <img src="@/assets/image/klg/u2538.svg" />
          <span class="header-right-text">重新编辑</span>
        </span>
      </div>
      <div class="base-info" style="margin: 5px 0">
        <span class="author-info">
          <span style="margin-right: 10px">作者</span>
          <span>{{ dataForm.author }}</span>
        </span>
        <span class="save-time">
          <span style="margin-right: 10px">保存时间</span>
          <span>{{ dataForm.saveTime }}</span>
        </span>
      </div>
      <div class="detail-info">
        <span class="detail-info-left">
          <span class="info-syn"
            ><span style="white-space: nowrap; margin-right: 40px">同义词</span>
            <div
              v-if="dataForm.synList.length === 0"
              style="white-space: nowrap; color: var(--color-grey)"
            >
              暂无同义词
            </div>
            <div v-else class="info-syn-text-block">
              <el-tag
                effect="plain"
                :disable-transitions="true"
                class="info-syn-text primary"
                v-for="(item, index) in dataForm.synList"
                :key="index"
              >
                <span class="ck-content" v-html="item"> </span>
              </el-tag>
            </div>
          </span>
          <span class="info-syn">
            <span style="white-space: nowrap; margin-right: 26px">所属领域</span>
            <div class="info-syn-text-block" style="margin-top: 5px">
              <el-tag
                effect="plain"
                :disable-transitions="true"
                class="info-syn-text primary"
                v-for="(item, index) in dataForm.areaList"
                :key="index"
              >
                <span class="ck-content" v-html="item.label"> </span>
              </el-tag>
            </div>
          </span>
        </span>
        <span class="detail-info-right" @click="handleDrawer">
          <img src="@/assets/image/klg/u1695.svg" />
          <span class="detail-info-right-text">查看编者笔记</span>
        </span>
      </div>
      <div style="margin-top: 5px" class="line"></div>
      <div class="main-container">
        <div class="content-container">
          <template v-if="wordContent">
            <div v-html="wordContent" class="lineWordContent ck-content" id="underline"></div>
          </template>
        </div>
        <div class="proof-container" v-if="dataForm.type === KlgType.Principles">
          <div class="proof-container-title unlineWordContent">证明过程</div>
          <div class="proof-block-list lineWordContent">
            <!-- {{ wordStore.getProofContent() }} -->
            <div
              class="proof-block"
              v-for="(block, blockIndex) in dataForm.proofList"
              :key="blockIndex"
            >
              <div
                class="proof-block-item"
                v-for="(cond, condIndex) in block.klgProofCondList"
                :key="condIndex"
              >
                <!-- <span class="proof-item-label unlineWordContent">
                {{ cond.sort !== undefined ? findKeyByValue(cond.sort, proofCondTypeDict): "条件" }}
              </span> -->
                <span class="proof-item-content">
                  <span v-html="cond.cnt" class="ck-content renderItem"> </span
                ></span>
              </div>
              <div class="proof-block-item">
                <!-- <span class="proof-item-label unlineWordContent" >论证结论</span> -->
                <span class="proof-item-content"
                  ><span
                    v-html="block.conclusion"
                    id="htmlContent"
                    class="ck-content renderItem"
                  ></span
                ></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="down-wrapper">
      <div class="ref-container-title">参考文献</div>
      <span v-if="dataForm.refList.length === 0" class="ref-list" style="color: var(--color-grey)"
        >暂无文献</span
      >
      <div v-else class="ref-list">
        <span class="ref-line" v-for="ref in dataForm.refList" :key="ref.refId">
          <span class="ref-line-name">{{ ref.cntName }}</span>
          <span class="ref-line-chapter">{{ ref.indexPage ? ref.indexPage : '暂无信息' }}</span>
        </span>
      </div>
    </div>
  </div>
  <!-- other -->
  <NoteDrawer ref="drawerRef"></NoteDrawer>
  <QuestionDetailDrawer />
  <div
    ref="floatingElement"
    :style="
      floatingVisible ? floatingStyles : { position: 'fixed', top: '-9999px', left: '-9999px' }
    "
    class="floatingContainer"
  >
    <Transition name="scale">
      <div
        v-if="floatingVisible && sameQuestionList && sameQuestionList.length > 0"
        class="floating-content"
      >
        <div
          v-for="question in sameQuestionList"
          :key="question.questionId"
          class="floating-content-item"
          @click="handleFloatingQuestionClick(question)"
        >
          <div style="display: flex; align-items: center">
            <span class="keyword-container">
              【

              <span
                class="questionList ellipsis-text-inline"
                style="word-break: break-all"
                v-html="question.keyword"
              ></span>

              】
            </span>
            <span v-if="question.questionType != '开放性问题'">{{ question.questionType }}</span>
            <span v-else v-html="question.questionDescription"></span>
          </div>
        </div>
      </div>
    </Transition>
  </div>
  <div
    v-if="questionIconVisible"
    ref="questionIconElement"
    class="question-icon"
    :style="{
      position: 'fixed',
      left: questionIconPosition.x + 'px',
      top: questionIconPosition.y + 'px',
      zIndex: 10000
    }"
    @click="handleQuestionIconClick"
  >
    <!-- 悬浮提示 -->
    <div class="question-tooltip">提问</div>
    <!-- 问号图标 -->
    <div class="question-icon-circle">
      <img src="@/assets/question.svg" alt="" />
    </div>
  </div>
  <div
    v-if="questionIconVisible"
    ref="questionIconElement"
    class="question-icon"
    :style="{
      position: 'fixed',
      left: questionIconPosition.x + 'px',
      top: questionIconPosition.y + 'px',
      zIndex: 10000
    }"
    @click="handleQuestionIconClick"
  >
    <!-- 悬浮提示 -->
    <div class="question-tooltip">提问</div>
    <!-- 问号图标 -->
    <div class="question-icon-circle">
      <img src="@/assets/question.svg" alt="" />
    </div>
  </div>
</template>

<style scoped src="./css/KlgInfo.less"></style>
